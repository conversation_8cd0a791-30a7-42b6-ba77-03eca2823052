{"name": "sydney-agents", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build", "start": "mastra start"}, "keywords": [], "author": "", "license": "ISC", "description": "", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@mastra/core": "^0.10.5", "@mastra/evals": "^0.10.3", "@mastra/fastembed": "^0.10.0", "@mastra/libsql": "^0.11.0", "@mastra/loggers": "^0.10.2", "@mastra/mcp": "^0.10.3", "@mastra/memory": "^0.10.3", "@mastra/voice-elevenlabs": "^0.10.2", "@mastra/voice-google": "^0.10.2", "@mastra/voice-openai": "^0.10.2", "@modelcontextprotocol/sdk": "^1.12.3", "@types/express": "^5.0.3", "@types/ws": "^8.18.1", "axios": "^1.10.0", "express": "^5.1.0", "node-fetch": "^3.3.2", "playwright": "^1.53.1", "ws": "^8.18.3", "zod": "^3.25.65"}, "devDependencies": {"@types/node": "^24.0.3", "mastra": "^0.10.5", "typescript": "^5.8.3"}}