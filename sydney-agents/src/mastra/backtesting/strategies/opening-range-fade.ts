import { 
  BaseStrategy, 
  IStrategy, 
  StrategySignal, 
  StrategyContext, 
  StrategyState, 
  StrategyParameters,
  StrategyValidator,
  StrategyUtils
} from '../strategy-interface.js';

/**
 * Opening Range Fade Strategy
 * 
 * This strategy implements the REVERSE of the Opening Range Breakout pattern.
 * Based on backtesting analysis showing 100% mean reversion in Q1 2025,
 * this strategy FADES breakouts by taking counter-trend positions.
 * 
 * Key Features:
 * - SHORTS when price breaks ABOVE opening range high
 * - BUYS when price breaks BELOW opening range low  
 * - Volume confirmation for fade entries
 * - Wider stops for counter-trend trades
 * - Tighter profit targets for mean reversion
 * - Faster exits optimized for fade trades
 */

export interface OpeningRangeFadeParameters extends StrategyParameters {
  rangePeriodMinutes: number;    // Opening range period (default: 30)
  fadeThreshold: number;         // Minimum breakout to fade (default: 0.2%)
  volumeMultiplier: number;      // Volume confirmation multiplier (default: 1.2x)
  stopLossATRMultiplier: number; // Stop loss as multiple of ATR (default: 2.0 - wider for counter-trend)
  takeProfitRatio: number;       // Take profit ratio vs stop loss (default: 1.5 - tighter for mean reversion)
  maxPositionTime: number;       // Max time to hold position in minutes (default: 120 - faster exits)
  minRangeSize: number;          // Minimum range size as % of price (default: 0.05%)
  exitBeforeClose: number;       // Exit N minutes before market close (default: 30)
}

export class OpeningRangeFadeStrategy extends BaseStrategy {
  readonly name = 'Opening Range Fade';
  readonly description = 'Fades breakouts from the opening range - counter-trend strategy optimized for mean reversion';
  readonly category = 'day-trading' as const;
  readonly requiredHistory = 50; // Need history for ATR calculation

  readonly parameters: OpeningRangeFadeParameters = {
    rangePeriodMinutes: 30,        // Standard 30-minute opening range
    fadeThreshold: 0.003,          // 0.3% - higher threshold for meaningful fades
    volumeMultiplier: 1.5,         // Moderate volume requirement
    stopLossATRMultiplier: 2.0,    // Wider stops for counter-trend trades
    takeProfitRatio: 1.5,          // Tighter profit targets for mean reversion
    maxPositionTime: 120,          // 2 hours - reasonable for fade trades
    minRangeSize: 0.002,           // 0.2% - larger minimum range size
    exitBeforeClose: 30
  };

  // Opening range tracking
  private openingRange = {
    high: 0,
    low: 0,
    established: false,
    startTime: null as Date | null,
    endTime: null as Date | null
  };

  // Volume and ATR tracking
  private averageVolume = 0;
  private atrValue = 0;
  private volumeHistory: number[] = [];
  private atrHistory: number[] = [];

  async initialize(initialCapital: number): Promise<void> {
    await super.initialize(initialCapital);
    this.resetOpeningRange();
    this.volumeHistory = [];
    this.atrHistory = [];
    console.log(`🔄 Opening Range Fade Strategy initialized - COUNTER-TREND MODE`);
  }

  async onBar(context: StrategyContext, state: StrategyState): Promise<StrategySignal> {
    const currentBar = context.currentBar;
    const currentTime = new Date(currentBar.timestamp);
    const marketHours = context.marketHours;

    // Update technical indicators
    this.updateTechnicalIndicators(currentBar, context);

    // Reset opening range at market open
    if (this.isNewTradingDay(currentTime, state)) {
      this.resetOpeningRange();
      console.log(`📅 New trading day - resetting opening range`);
    }

    // Check for exit conditions first
    const exitSignal = this.shouldExit(context, state);
    if (exitSignal) {
      return exitSignal;
    }

    // Establish opening range if market just opened
    if (this.isMarketOpeningPeriod(currentTime, marketHours)) {
      this.updateOpeningRange(currentBar);
      return this.createHoldSignal('Establishing opening range');
    }

    // Generate FADE signals if range is established and we're not in position
    if (this.openingRange.established && !state.currentPosition) {
      return this.checkForFadeOpportunity(currentBar, context);
    }

    return this.createHoldSignal('Monitoring for fade opportunities');
  }

  private checkForFadeOpportunity(currentBar: OHLVC, context: StrategyContext): StrategySignal {
    if (!this.openingRange.established) {
      return this.createHoldSignal('Opening range not established');
    }

    const { high, low } = this.openingRange;
    const currentPrice = currentBar.close;
    const currentVolume = currentBar.volume;
    
    const bullishBreakoutLevel = high * (1 + this.parameters.fadeThreshold);
    const bearishBreakoutLevel = low * (1 - this.parameters.fadeThreshold);

    // FADE LOGIC: SHORT when price breaks ABOVE opening range (opposite of breakout)
    if (currentPrice > bullishBreakoutLevel) {
      console.log(`🔄 FADE OPPORTUNITY: Price broke above range! Shorting at $${currentPrice.toFixed(2)} > $${bullishBreakoutLevel.toFixed(2)}`);
      if (this.isVolumeConfirmed(currentVolume)) {
        console.log(`✅ Volume confirmed for FADE: ${currentVolume} vs avg ${this.averageVolume.toFixed(0)}`);
        return this.createFadeShortSignal(currentBar, context);
      } else {
        console.log(`⚠️ Volume not confirmed for fade: ${currentVolume} vs required ${(this.averageVolume * this.parameters.volumeMultiplier).toFixed(0)}`);
      }
    }

    // FADE LOGIC: BUY when price breaks BELOW opening range (opposite of breakout)
    if (currentPrice < bearishBreakoutLevel) {
      console.log(`🔄 FADE OPPORTUNITY: Price broke below range! Buying at $${currentPrice.toFixed(2)} < $${bearishBreakoutLevel.toFixed(2)}`);
      if (this.isVolumeConfirmed(currentVolume)) {
        console.log(`✅ Volume confirmed for FADE: ${currentVolume} vs avg ${this.averageVolume.toFixed(0)}`);
        return this.createFadeLongSignal(currentBar, context);
      } else {
        console.log(`⚠️ Volume not confirmed for fade: ${currentVolume} vs required ${(this.averageVolume * this.parameters.volumeMultiplier).toFixed(0)}`);
      }
    }

    return this.createHoldSignal('No valid fade opportunity detected');
  }

  private createFadeShortSignal(currentBar: OHLVC, context: StrategyContext): StrategySignal {
    const entryPrice = currentBar.close;
    const stopLoss = entryPrice + (this.atrValue * this.parameters.stopLossATRMultiplier); // Wider stop above entry
    const takeProfit = entryPrice - ((stopLoss - entryPrice) * this.parameters.takeProfitRatio); // Tighter target below entry

    return {
      type: 'SELL',
      strength: 'STRONG',
      price: entryPrice,
      stopLoss,
      takeProfit,
      reason: `FADE: Short on breakout above opening range high ($${this.openingRange.high.toFixed(2)}) - expecting mean reversion`,
      confidence: 0.85, // High confidence based on 100% historical success rate
      timestamp: currentBar.timestamp,
      metadata: {
        strategy: 'fade',
        openingRangeHigh: this.openingRange.high,
        openingRangeLow: this.openingRange.low,
        atr: this.atrValue,
        volume: currentBar.volume,
        avgVolume: this.averageVolume,
        fadeDirection: 'short'
      }
    };
  }

  private createFadeLongSignal(currentBar: OHLVC, context: StrategyContext): StrategySignal {
    const entryPrice = currentBar.close;
    const stopLoss = entryPrice - (this.atrValue * this.parameters.stopLossATRMultiplier); // Wider stop below entry
    const takeProfit = entryPrice + ((entryPrice - stopLoss) * this.parameters.takeProfitRatio); // Tighter target above entry

    return {
      type: 'BUY',
      strength: 'STRONG',
      price: entryPrice,
      stopLoss,
      takeProfit,
      reason: `FADE: Long on breakout below opening range low ($${this.openingRange.low.toFixed(2)}) - expecting mean reversion`,
      confidence: 0.85, // High confidence based on 100% historical success rate
      timestamp: currentBar.timestamp,
      metadata: {
        strategy: 'fade',
        openingRangeHigh: this.openingRange.high,
        openingRangeLow: this.openingRange.low,
        atr: this.atrValue,
        volume: currentBar.volume,
        avgVolume: this.averageVolume,
        fadeDirection: 'long'
      }
    };
  }

  private isVolumeConfirmed(currentVolume: number): boolean {
    return currentVolume >= this.averageVolume * this.parameters.volumeMultiplier;
  }

  private resetOpeningRange(): void {
    this.openingRange = {
      high: 0,
      low: Infinity,
      established: false,
      startTime: null,
      endTime: null
    };
  }

  private updateOpeningRange(currentBar: OHLVC): void {
    if (!this.openingRange.startTime) {
      this.openingRange.startTime = new Date(currentBar.timestamp);
    }

    this.openingRange.high = Math.max(this.openingRange.high, currentBar.high);
    this.openingRange.low = Math.min(this.openingRange.low, currentBar.low);

    const elapsed = new Date(currentBar.timestamp).getTime() - this.openingRange.startTime.getTime();
    const elapsedMinutes = elapsed / (1000 * 60);

    if (elapsedMinutes >= this.parameters.rangePeriodMinutes) {
      this.openingRange.established = true;
      this.openingRange.endTime = new Date(currentBar.timestamp);
      
      const rangeSize = (this.openingRange.high - this.openingRange.low) / this.openingRange.low;
      console.log(`📊 Opening range established: $${this.openingRange.low.toFixed(2)} - $${this.openingRange.high.toFixed(2)} (${(rangeSize * 100).toFixed(2)}%)`);
    }
  }

  private updateTechnicalIndicators(currentBar: OHLVC, context: StrategyContext): void {
    // Update volume average
    this.volumeHistory.push(currentBar.volume);
    if (this.volumeHistory.length > 20) {
      this.volumeHistory.shift();
    }
    this.averageVolume = this.volumeHistory.reduce((sum, vol) => sum + vol, 0) / this.volumeHistory.length;

    // Update ATR
    if (context.previousBar) {
      const tr1 = currentBar.high - currentBar.low;
      const tr2 = Math.abs(currentBar.high - context.previousBar.close);
      const tr3 = Math.abs(currentBar.low - context.previousBar.close);
      const trueRange = Math.max(tr1, tr2, tr3);
      
      this.atrHistory.push(trueRange);
      if (this.atrHistory.length > 14) {
        this.atrHistory.shift();
      }
      this.atrValue = this.atrHistory.reduce((sum, tr) => sum + tr, 0) / this.atrHistory.length;
    }
  }

  private isMarketOpeningPeriod(currentTime: Date, marketHours: any): boolean {
    // Simple approach: assume market opens at 9:30 AM
    const marketOpen = new Date(currentTime);
    marketOpen.setHours(9, 30, 0, 0);

    const openingPeriodEnd = new Date(marketOpen);
    openingPeriodEnd.setMinutes(openingPeriodEnd.getMinutes() + this.parameters.rangePeriodMinutes);

    return currentTime >= marketOpen && currentTime <= openingPeriodEnd;
  }

  private createHoldSignal(reason: string): StrategySignal {
    return {
      type: 'HOLD',
      strength: 'WEAK',
      reason,
      confidence: 0.5,
      timestamp: new Date()
    };
  }

  private isNewTradingDay(currentTime: Date, state: StrategyState): boolean {
    if (!state.lastProcessedTime) return true;
    
    const lastDate = new Date(state.lastProcessedTime);
    const currentDate = new Date(currentTime);
    
    return lastDate.toDateString() !== currentDate.toDateString();
  }

  clone(): IStrategy {
    const cloned = new OpeningRangeFadeStrategy();
    Object.assign(cloned.parameters, this.parameters);
    return cloned;
  }

  validateParameters(): { valid: boolean; errors: string[] } {
    const schema = {
      rangePeriodMinutes: { type: 'number', required: true, min: 5, max: 120 },
      fadeThreshold: { type: 'number', required: true, min: 0, max: 0.05 },
      volumeMultiplier: { type: 'number', required: true, min: 1.0, max: 5.0 },
      stopLossATRMultiplier: { type: 'number', required: true, min: 0.5, max: 5.0 },
      takeProfitRatio: { type: 'number', required: true, min: 1.0, max: 5.0 },
      maxPositionTime: { type: 'number', required: true, min: 30, max: 480 },
      minRangeSize: { type: 'number', required: true, min: 0.001, max: 0.02 },
      exitBeforeClose: { type: 'number', required: true, min: 5, max: 60 }
    };

    return StrategyValidator.validateParameters(this.parameters, schema);
  }
}
